#include "PerformanceProfiler.hpp"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QTextStream>
#include <QFile>
#include <QUuid>
#include <QProcess>
#include <QScreen>
#include <QStyleOption>
#include <algorithm>
#include <numeric>
#include <cmath>

namespace DeclarativeUI::Profiling {

// **PerformanceProfiler Implementation**

PerformanceProfiler::PerformanceProfiler(QObject* parent)
    : QObject(parent)
{
    // Initialize timers
    collection_timer_ = std::make_unique<QTimer>(this);
    analysis_timer_ = std::make_unique<QTimer>(this);
    
    connect(collection_timer_.get(), &QTimer::timeout, this, &PerformanceProfiler::collectMetrics);
    connect(analysis_timer_.get(), &QTimer::timeout, this, &PerformanceProfiler::analyzePerformance);
    
    // Set default configuration
    config_.sampling_interval_ms = 100;
    config_.max_history_size = 10000;
    config_.auto_save_reports = true;
    config_.reports_directory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/profiling_reports";
    
    // Create reports directory
    QDir().mkpath(config_.reports_directory);
    
    // Generate session ID
    current_session_id_ = generateSessionId();
    
    // Initialize platform-specific resources
#ifdef Q_OS_WIN
    process_handle_ = GetCurrentProcess();
    
    // Initialize PDH for CPU monitoring
    PdhOpenQuery(nullptr, 0, &cpu_query_);
    PdhAddCounter(cpu_query_, L"\\Processor(_Total)\\% Processor Time", 0, &cpu_counter_);
    PdhCollectQueryData(cpu_query_);
#endif
    
    qDebug() << "Performance Profiler initialized with session ID:" << current_session_id_;
}

PerformanceProfiler::~PerformanceProfiler() {
    stopProfiling();
    
#ifdef Q_OS_WIN
    if (cpu_query_) {
        PdhCloseQuery(cpu_query_);
    }
#endif
}

void PerformanceProfiler::startProfiling() {
    if (profiling_active_.load()) {
        qWarning() << "Profiling is already active";
        return;
    }
    
    profiling_active_.store(true);
    profiling_paused_.store(false);
    session_timer_.start();
    
    // Start data collection
    collection_timer_->start(config_.sampling_interval_ms);
    analysis_timer_->start(config_.sampling_interval_ms * 10); // Analyze every 10 samples
    
    emit profilingStarted();
    qDebug() << "Performance profiling started";
}

void PerformanceProfiler::stopProfiling() {
    if (!profiling_active_.load()) {
        return;
    }
    
    profiling_active_.store(false);
    collection_timer_->stop();
    analysis_timer_->stop();
    
    // Auto-save report if enabled
    if (config_.auto_save_reports) {
        QString report_path = config_.reports_directory + "/" + 
                             current_session_id_ + "_performance_report.json";
        exportReport(report_path, "json");
    }
    
    emit profilingStopped();
    qDebug() << "Performance profiling stopped";
}

void PerformanceProfiler::pauseProfiling() {
    profiling_paused_.store(true);
    collection_timer_->stop();
    qDebug() << "Performance profiling paused";
}

void PerformanceProfiler::resumeProfiling() {
    if (profiling_active_.load()) {
        profiling_paused_.store(false);
        collection_timer_->start(config_.sampling_interval_ms);
        qDebug() << "Performance profiling resumed";
    }
}

void PerformanceProfiler::setConfiguration(const ProfilerConfig& config) {
    config_ = config;
    
    // Update timer intervals if profiling is active
    if (profiling_active_.load() && !profiling_paused_.load()) {
        collection_timer_->setInterval(config_.sampling_interval_ms);
        analysis_timer_->setInterval(config_.sampling_interval_ms * 10);
    }
    
    // Create reports directory
    QDir().mkpath(config_.reports_directory);
}

PerformanceProfiler::PerformanceSnapshot PerformanceProfiler::takeSnapshot() {
    PerformanceSnapshot snapshot;
    snapshot.timestamp = QDateTime::currentDateTime();
    snapshot.session_id = current_session_id_;
    
    if (config_.enable_cpu_profiling) {
        snapshot.cpu = getCurrentCPUMetrics();
    }
    
    if (config_.enable_memory_profiling) {
        snapshot.memory = getCurrentMemoryMetrics();
    }
    
    if (config_.enable_render_profiling) {
        snapshot.render = getCurrentRenderMetrics();
    }
    
    if (config_.enable_io_profiling) {
        snapshot.io = getCurrentIOMetrics();
    }
    
    if (config_.enable_thread_profiling) {
        snapshot.threads = getCurrentThreadMetrics();
    }
    
    return snapshot;
}

std::vector<PerformanceProfiler::PerformanceSnapshot> PerformanceProfiler::getHistory() const {
    QReadLocker locker(&history_lock_);
    return performance_history_;
}

std::vector<PerformanceProfiler::PerformanceSnapshot> PerformanceProfiler::getHistory(
    const QDateTime& start, const QDateTime& end) const {
    
    QReadLocker locker(&history_lock_);
    std::vector<PerformanceSnapshot> filtered_history;
    
    std::copy_if(performance_history_.begin(), performance_history_.end(),
                std::back_inserter(filtered_history),
                [&start, &end](const PerformanceSnapshot& snapshot) {
                    return snapshot.timestamp >= start && snapshot.timestamp <= end;
                });
    
    return filtered_history;
}

void PerformanceProfiler::clearHistory() {
    QWriteLocker locker(&history_lock_);
    performance_history_.clear();
    detected_bottlenecks_.clear();
    qDebug() << "Performance history cleared";
}

PerformanceProfiler::CPUMetrics PerformanceProfiler::getCurrentCPUMetrics() {
    return collectCPUMetrics();
}

PerformanceProfiler::MemoryMetrics PerformanceProfiler::getCurrentMemoryMetrics() {
    return collectMemoryMetrics();
}

PerformanceProfiler::RenderMetrics PerformanceProfiler::getCurrentRenderMetrics() {
    return collectRenderMetrics();
}

PerformanceProfiler::IOMetrics PerformanceProfiler::getCurrentIOMetrics() {
    return collectIOMetrics();
}

PerformanceProfiler::ThreadMetrics PerformanceProfiler::getCurrentThreadMetrics() {
    return collectThreadMetrics();
}

void PerformanceProfiler::collectMetrics() {
    if (!profiling_active_.load() || profiling_paused_.load()) {
        return;
    }
    
    PerformanceSnapshot snapshot = takeSnapshot();
    
    {
        QWriteLocker locker(&history_lock_);
        performance_history_.push_back(snapshot);
        
        // Limit history size
        if (performance_history_.size() > static_cast<size_t>(config_.max_history_size)) {
            performance_history_.erase(performance_history_.begin());
        }
    }
    
    emit snapshotTaken(snapshot);
    
    // Check thresholds
    checkThresholds();
}

void PerformanceProfiler::analyzePerformance() {
    if (!profiling_active_.load() || profiling_paused_.load()) {
        return;
    }
    
    // Detect bottlenecks
    auto bottlenecks = detectBottlenecks();
    for (const auto& bottleneck : bottlenecks) {
        emit bottleneckDetected(bottleneck);
    }
    
    // Generate optimization recommendations
    auto recommendations = generateOptimizationRecommendations();
    for (const auto& recommendation : recommendations) {
        emit optimizationRecommendation(recommendation);
    }
}

void PerformanceProfiler::checkThresholds() {
    QReadLocker locker(&history_lock_);
    
    if (performance_history_.empty()) {
        return;
    }
    
    const auto& latest = performance_history_.back();
    
    // Check CPU threshold
    if (latest.cpu.usage_percentage > config_.cpu_warning_threshold) {
        QJsonObject details;
        details["cpu_usage"] = latest.cpu.usage_percentage;
        details["threshold"] = config_.cpu_warning_threshold;
        emit performanceWarning("High CPU usage detected", details);
    }
    
    // Check memory threshold
    if (latest.memory.used_memory_kb / 1024 > config_.memory_warning_threshold_mb) {
        QJsonObject details;
        details["memory_usage_mb"] = latest.memory.used_memory_kb / 1024;
        details["threshold_mb"] = config_.memory_warning_threshold_mb;
        emit performanceWarning("High memory usage detected", details);
    }
    
    // Check frame rate threshold
    if (latest.render.frame_rate < config_.frame_rate_warning_threshold) {
        QJsonObject details;
        details["frame_rate"] = latest.render.frame_rate;
        details["threshold"] = config_.frame_rate_warning_threshold;
        emit performanceWarning("Low frame rate detected", details);
    }
}

PerformanceProfiler::CPUMetrics PerformanceProfiler::collectCPUMetrics() {
    CPUMetrics metrics;
    metrics.timestamp = QDateTime::currentDateTime();
    
#ifdef Q_OS_WIN
    // Windows implementation
    PDH_FMT_COUNTERVALUE counter_value;
    PdhCollectQueryData(cpu_query_);
    PdhGetFormattedCounterValue(cpu_counter_, PDH_FMT_DOUBLE, nullptr, &counter_value);
    metrics.usage_percentage = counter_value.doubleValue;
    
    FILETIME idle_time, kernel_time, user_time;
    if (GetSystemTimes(&idle_time, &kernel_time, &user_time)) {
        // Calculate user and system time percentages
        ULARGE_INTEGER kernel_time_int, user_time_int;
        kernel_time_int.LowPart = kernel_time.dwLowDateTime;
        kernel_time_int.HighPart = kernel_time.dwHighDateTime;
        user_time_int.LowPart = user_time.dwLowDateTime;
        user_time_int.HighPart = user_time.dwHighDateTime;
        
        ULARGE_INTEGER total_time;
        total_time.QuadPart = kernel_time_int.QuadPart + user_time_int.QuadPart;
        
        if (total_time.QuadPart > 0) {
            metrics.system_time_percentage = (double)kernel_time_int.QuadPart / total_time.QuadPart * 100.0;
            metrics.user_time_percentage = (double)user_time_int.QuadPart / total_time.QuadPart * 100.0;
        }
    }
    
#elif defined(Q_OS_LINUX)
    // Linux implementation
    std::ifstream stat_file("/proc/stat");
    if (stat_file.is_open()) {
        std::string line;
        std::getline(stat_file, line);
        
        // Parse CPU times from /proc/stat
        std::istringstream iss(line);
        std::string cpu_label;
        long user, nice, system, idle, iowait, irq, softirq, steal;
        
        iss >> cpu_label >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal;
        
        long total_time = user + nice + system + idle + iowait + irq + softirq + steal;
        long active_time = total_time - idle - iowait;
        
        if (total_time > 0) {
            metrics.usage_percentage = (double)active_time / total_time * 100.0;
            metrics.user_time_percentage = (double)(user + nice) / total_time * 100.0;
            metrics.system_time_percentage = (double)(system + irq + softirq) / total_time * 100.0;
        }
    }
    
    // Get context switches and page faults
    std::ifstream vmstat_file("/proc/vmstat");
    if (vmstat_file.is_open()) {
        std::string line;
        while (std::getline(vmstat_file, line)) {
            if (line.find("pgfault") == 0) {
                std::istringstream iss(line);
                std::string label;
                iss >> label >> metrics.page_faults;
            }
        }
    }
    
#elif defined(Q_OS_MACOS)
    // macOS implementation
    host_cpu_load_info_data_t cpu_info;
    mach_msg_type_number_t count = HOST_CPU_LOAD_INFO_COUNT;
    
    if (host_statistics(mach_host_self(), HOST_CPU_LOAD_INFO, 
                       (host_info_t)&cpu_info, &count) == KERN_SUCCESS) {
        
        unsigned int total_ticks = cpu_info.cpu_ticks[CPU_STATE_USER] +
                                  cpu_info.cpu_ticks[CPU_STATE_SYSTEM] +
                                  cpu_info.cpu_ticks[CPU_STATE_IDLE] +
                                  cpu_info.cpu_ticks[CPU_STATE_NICE];
        
        if (total_ticks > 0) {
            metrics.usage_percentage = (double)(total_ticks - cpu_info.cpu_ticks[CPU_STATE_IDLE]) / total_ticks * 100.0;
            metrics.user_time_percentage = (double)(cpu_info.cpu_ticks[CPU_STATE_USER] + cpu_info.cpu_ticks[CPU_STATE_NICE]) / total_ticks * 100.0;
            metrics.system_time_percentage = (double)cpu_info.cpu_ticks[CPU_STATE_SYSTEM] / total_ticks * 100.0;
        }
    }
#endif
    
    return metrics;
}

PerformanceProfiler::MemoryMetrics PerformanceProfiler::collectMemoryMetrics() {
    MemoryMetrics metrics;
    metrics.timestamp = QDateTime::currentDateTime();
    
#ifdef Q_OS_WIN
    // Windows implementation
    PROCESS_MEMORY_COUNTERS_EX pmc;
    if (GetProcessMemoryInfo(process_handle_, (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
        metrics.used_memory_kb = pmc.WorkingSetSize / 1024;
        metrics.virtual_memory_kb = pmc.PagefileUsage / 1024;
        metrics.heap_memory_kb = pmc.PrivateUsage / 1024;
    }
    
    MEMORYSTATUSEX mem_status;
    mem_status.dwLength = sizeof(mem_status);
    if (GlobalMemoryStatusEx(&mem_status)) {
        metrics.total_memory_kb = mem_status.ullTotalPhys / 1024;
        metrics.free_memory_kb = mem_status.ullAvailPhys / 1024;
    }
    
#elif defined(Q_OS_LINUX)
    // Linux implementation
    std::ifstream status_file("/proc/self/status");
    if (status_file.is_open()) {
        std::string line;
        while (std::getline(status_file, line)) {
            if (line.find("VmRSS:") == 0) {
                std::istringstream iss(line);
                std::string label, unit;
                iss >> label >> metrics.resident_memory_kb >> unit;
            } else if (line.find("VmSize:") == 0) {
                std::istringstream iss(line);
                std::string label, unit;
                iss >> label >> metrics.virtual_memory_kb >> unit;
            }
        }
    }
    
    std::ifstream meminfo_file("/proc/meminfo");
    if (meminfo_file.is_open()) {
        std::string line;
        while (std::getline(meminfo_file, line)) {
            if (line.find("MemTotal:") == 0) {
                std::istringstream iss(line);
                std::string label, unit;
                iss >> label >> metrics.total_memory_kb >> unit;
            } else if (line.find("MemAvailable:") == 0) {
                std::istringstream iss(line);
                std::string label, unit;
                iss >> label >> metrics.free_memory_kb >> unit;
            }
        }
    }
    
#elif defined(Q_OS_MACOS)
    // macOS implementation
    task_basic_info_data_t task_info;
    mach_msg_type_number_t count = TASK_BASIC_INFO_COUNT;
    
    if (task_info(mach_task_self(), TASK_BASIC_INFO, 
                  (task_info_t)&task_info, &count) == KERN_SUCCESS) {
        metrics.resident_memory_kb = task_info.resident_size / 1024;
        metrics.virtual_memory_kb = task_info.virtual_size / 1024;
    }
    
    // Get system memory info
    int mib[2] = {CTL_HW, HW_MEMSIZE};
    uint64_t physical_memory;
    size_t length = sizeof(physical_memory);
    if (sysctl(mib, 2, &physical_memory, &length, nullptr, 0) == 0) {
        metrics.total_memory_kb = physical_memory / 1024;
    }
#endif
    
    // Calculate derived metrics
    metrics.used_memory_kb = metrics.total_memory_kb - metrics.free_memory_kb;
    
    if (metrics.total_memory_kb > 0) {
        metrics.fragmentation_ratio = (double)metrics.used_memory_kb / metrics.total_memory_kb;
    }
    
    return metrics;
}

PerformanceProfiler::RenderMetrics PerformanceProfiler::collectRenderMetrics() {
    RenderMetrics metrics;
    metrics.timestamp = QDateTime::currentDateTime();
    
    // Get application-wide render metrics
    QApplication* app = qobject_cast<QApplication*>(QApplication::instance());
    if (!app) {
        return metrics;
    }
    
    // Count visible widgets
    QWidgetList all_widgets = app->allWidgets();
    for (QWidget* widget : all_widgets) {
        if (widget && widget->isVisible()) {
            metrics.visible_widgets++;
        }
    }
    
    // Get screen information
    QScreen* primary_screen = app->primaryScreen();
    if (primary_screen) {
        metrics.frame_rate = primary_screen->refreshRate();
    }
    
    // Estimate frame time based on refresh rate
    if (metrics.frame_rate > 0) {
        metrics.frame_time_ms = static_cast<qint64>(1000.0 / metrics.frame_rate);
    }
    
    return metrics;
}

PerformanceProfiler::IOMetrics PerformanceProfiler::collectIOMetrics() {
    IOMetrics metrics;
    metrics.timestamp = QDateTime::currentDateTime();
    
#ifdef Q_OS_WIN
    // Windows implementation
    IO_COUNTERS io_counters;
    if (GetProcessIoCounters(process_handle_, &io_counters)) {
        metrics.read_bytes = io_counters.ReadTransferCount;
        metrics.write_bytes = io_counters.WriteTransferCount;
        metrics.read_operations = io_counters.ReadOperationCount;
        metrics.write_operations = io_counters.WriteOperationCount;
    }
    
#elif defined(Q_OS_LINUX)
    // Linux implementation
    std::ifstream io_file("/proc/self/io");
    if (io_file.is_open()) {
        std::string line;
        while (std::getline(io_file, line)) {
            if (line.find("read_bytes:") == 0) {
                std::istringstream iss(line);
                std::string label;
                iss >> label >> metrics.read_bytes;
            } else if (line.find("write_bytes:") == 0) {
                std::istringstream iss(line);
                std::string label;
                iss >> label >> metrics.write_bytes;
            } else if (line.find("syscr:") == 0) {
                std::istringstream iss(line);
                std::string label;
                iss >> label >> metrics.read_operations;
            } else if (line.find("syscw:") == 0) {
                std::istringstream iss(line);
                std::string label;
                iss >> label >> metrics.write_operations;
            }
        }
    }
#endif
    
    return metrics;
}

PerformanceProfiler::ThreadMetrics PerformanceProfiler::collectThreadMetrics() {
    ThreadMetrics metrics;
    metrics.timestamp = QDateTime::currentDateTime();
    
    // Get Qt thread pool information
    QThreadPool* global_pool = QThreadPool::globalInstance();
    if (global_pool) {
        metrics.thread_count = global_pool->maxThreadCount();
        metrics.active_threads = global_pool->activeThreadCount();
        
        if (metrics.thread_count > 0) {
            metrics.thread_pool_utilization = (double)metrics.active_threads / metrics.thread_count * 100.0;
        }
    }
    
    return metrics;
}

QString PerformanceProfiler::generateSessionId() {
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

// **RenderProfiler Implementation**

RenderProfiler::RenderProfiler(QObject* parent)
    : QObject(parent)
{
}

void RenderProfiler::startProfiling(QWidget* widget) {
    if (!widget) {
        qWarning() << "Cannot profile null widget";
        return;
    }

    QMutexLocker locker(&profiling_mutex_);

    profiling_enabled_[widget] = true;
    widget_statistics_[widget] = RenderStatistics{};
    frame_timers_[widget] = QElapsedTimer{};

    qDebug() << "Started render profiling for widget:" << widget->objectName();
}

void RenderProfiler::stopProfiling(QWidget* widget) {
    if (!widget) {
        return;
    }

    QMutexLocker locker(&profiling_mutex_);

    profiling_enabled_[widget] = false;

    qDebug() << "Stopped render profiling for widget:" << widget->objectName();
}

void RenderProfiler::profileSingleFrame(QWidget* widget) {
    if (!widget) {
        return;
    }

    QMutexLocker locker(&profiling_mutex_);

    if (!profiling_enabled_[widget]) {
        return;
    }

    RenderFrame frame;
    frame.frame_number = widget_statistics_[widget].total_frames + 1;
    frame.timestamp = QDateTime::currentDateTime();
    frame.frame_size = widget->size();

    // Measure frame time
    QElapsedTimer frame_timer;
    frame_timer.start();

    // Trigger repaint and measure
    widget->repaint();
    QApplication::processEvents();

    frame.frame_time_ms = frame_timer.elapsed();

    // Calculate FPS
    if (frame.frame_time_ms > 0) {
        double fps = 1000.0 / frame.frame_time_ms;
        frame.metadata["fps"] = fps;
    }

    // Update statistics
    auto& stats = widget_statistics_[widget];
    stats.frame_history.push_back(frame);
    stats.total_frames++;

    // Limit frame history size
    if (stats.frame_history.size() > 1000) {
        stats.frame_history.erase(stats.frame_history.begin());
    }

    // Calculate statistics
    if (!stats.frame_history.empty()) {
        std::vector<double> fps_values;
        for (const auto& f : stats.frame_history) {
            if (f.frame_time_ms > 0) {
                fps_values.push_back(1000.0 / f.frame_time_ms);
            }
        }

        if (!fps_values.empty()) {
            stats.average_fps = std::accumulate(fps_values.begin(), fps_values.end(), 0.0) / fps_values.size();
            stats.min_fps = *std::min_element(fps_values.begin(), fps_values.end());
            stats.max_fps = *std::max_element(fps_values.begin(), fps_values.end());

            // Calculate variance
            double mean = stats.average_fps;
            double variance = 0.0;
            for (double fps : fps_values) {
                variance += (fps - mean) * (fps - mean);
            }
            stats.frame_time_variance = variance / fps_values.size();
        }
    }

    emit frameProfiled(widget, frame);
}

RenderProfiler::RenderStatistics RenderProfiler::getStatistics(QWidget* widget) const {
    QMutexLocker locker(&profiling_mutex_);

    auto it = widget_statistics_.find(widget);
    if (it != widget_statistics_.end()) {
        return it->second;
    }

    return RenderStatistics{};
}

std::vector<RenderProfiler::RenderFrame> RenderProfiler::getFrameHistory(QWidget* widget) const {
    QMutexLocker locker(&profiling_mutex_);

    auto it = widget_statistics_.find(widget);
    if (it != widget_statistics_.end()) {
        return it->second.frame_history;
    }

    return {};
}

QJsonObject RenderProfiler::generateRenderReport(QWidget* widget) const {
    QJsonObject report;

    auto stats = getStatistics(widget);

    report["widget_name"] = widget ? widget->objectName() : "Unknown";
    report["widget_type"] = widget ? widget->metaObject()->className() : "Unknown";
    report["total_frames"] = static_cast<qint64>(stats.total_frames);
    report["dropped_frames"] = static_cast<qint64>(stats.dropped_frames);
    report["average_fps"] = stats.average_fps;
    report["min_fps"] = stats.min_fps;
    report["max_fps"] = stats.max_fps;
    report["frame_time_variance"] = stats.frame_time_variance;
    report["total_paint_time_ms"] = static_cast<qint64>(stats.total_paint_time_ms);
    report["total_layout_time_ms"] = static_cast<qint64>(stats.total_layout_time_ms);

    // Add frame history
    QJsonArray frame_array;
    for (const auto& frame : stats.frame_history) {
        QJsonObject frame_obj;
        frame_obj["frame_number"] = static_cast<qint64>(frame.frame_number);
        frame_obj["frame_time_ms"] = static_cast<qint64>(frame.frame_time_ms);
        frame_obj["paint_time_ms"] = static_cast<qint64>(frame.paint_time_ms);
        frame_obj["layout_time_ms"] = static_cast<qint64>(frame.layout_time_ms);
        frame_obj["draw_calls"] = frame.draw_calls;
        frame_obj["timestamp"] = frame.timestamp.toString(Qt::ISODate);
        frame_array.append(frame_obj);
    }
    report["frame_history"] = frame_array;

    return report;
}

std::vector<QString> RenderProfiler::detectRenderIssues(QWidget* widget) const {
    std::vector<QString> issues;
    auto stats = getStatistics(widget);

    // Check for low frame rate
    if (stats.average_fps < 30.0) {
        issues.push_back("Low average frame rate: " + QString::number(stats.average_fps, 'f', 1) + " FPS");
    }

    // Check for high frame time variance
    if (stats.frame_time_variance > 100.0) {
        issues.push_back("High frame time variance: " + QString::number(stats.frame_time_variance, 'f', 1));
    }

    // Check for dropped frames
    if (stats.dropped_frames > stats.total_frames * 0.05) { // More than 5% dropped
        issues.push_back("High dropped frame rate: " + QString::number(stats.dropped_frames) + " out of " + QString::number(stats.total_frames));
    }

    return issues;
}

QJsonObject RenderProfiler::analyzeFrameDrops(QWidget* widget) const {
    QJsonObject analysis;
    auto stats = getStatistics(widget);

    analysis["total_frames"] = static_cast<qint64>(stats.total_frames);
    analysis["dropped_frames"] = static_cast<qint64>(stats.dropped_frames);

    if (stats.total_frames > 0) {
        double drop_rate = (double)stats.dropped_frames / stats.total_frames * 100.0;
        analysis["drop_rate_percentage"] = drop_rate;

        if (drop_rate > 5.0) {
            analysis["severity"] = "high";
        } else if (drop_rate > 1.0) {
            analysis["severity"] = "medium";
        } else {
            analysis["severity"] = "low";
        }
    }

    return analysis;
}

QJsonObject RenderProfiler::generateOptimizationSuggestions(QWidget* widget) const {
    QJsonObject suggestions;
    QJsonArray suggestion_array;

    auto stats = getStatistics(widget);
    auto issues = detectRenderIssues(widget);

    for (const QString& issue : issues) {
        QJsonObject suggestion;

        if (issue.contains("Low average frame rate")) {
            suggestion["issue"] = issue;
            suggestion["category"] = "Performance";
            suggestion["priority"] = "High";
            suggestion["suggestions"] = QJsonArray{
                "Reduce widget complexity",
                "Optimize paint events",
                "Use widget caching",
                "Minimize layout updates"
            };
        } else if (issue.contains("High frame time variance")) {
            suggestion["issue"] = issue;
            suggestion["category"] = "Consistency";
            suggestion["priority"] = "Medium";
            suggestion["suggestions"] = QJsonArray{
                "Avoid blocking operations in paint events",
                "Use asynchronous operations",
                "Optimize resource loading"
            };
        } else if (issue.contains("High dropped frame rate")) {
            suggestion["issue"] = issue;
            suggestion["category"] = "Reliability";
            suggestion["priority"] = "High";
            suggestion["suggestions"] = QJsonArray{
                "Reduce rendering complexity",
                "Implement frame pacing",
                "Use double buffering"
            };
        }

        suggestion_array.append(suggestion);
    }

    suggestions["widget_name"] = widget ? widget->objectName() : "Unknown";
    suggestions["suggestions"] = suggestion_array;
    suggestions["analysis_timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return suggestions;
}

// **MemoryLeakDetector Implementation**

MemoryLeakDetector& MemoryLeakDetector::instance() {
    static MemoryLeakDetector instance;
    return instance;
}

MemoryLeakDetector::MemoryLeakDetector(QObject* parent)
    : QObject(parent)
{
}

void MemoryLeakDetector::enableTracking(bool enabled) {
    tracking_enabled_.store(enabled);

    if (!enabled) {
        clearTracking();
    }

    qDebug() << "Memory leak tracking" << (enabled ? "enabled" : "disabled");
}

void MemoryLeakDetector::clearTracking() {
    QMutexLocker locker(&tracking_mutex_);
    allocations_.clear();
    total_allocated_.store(0);
}

void MemoryLeakDetector::trackAllocation(void* ptr, size_t size, const QString& source, int line) {
    if (!tracking_enabled_.load() || !ptr) {
        return;
    }

    QMutexLocker locker(&tracking_mutex_);

    AllocationInfo info;
    info.address = ptr;
    info.size = size;
    info.source_file = source;
    info.source_line = line;
    info.allocation_time = QDateTime::currentDateTime();

    allocations_[ptr] = info;
    total_allocated_.fetchAndAddAcquire(size);
}

void MemoryLeakDetector::trackDeallocation(void* ptr) {
    if (!tracking_enabled_.load() || !ptr) {
        return;
    }

    QMutexLocker locker(&tracking_mutex_);

    auto it = allocations_.find(ptr);
    if (it != allocations_.end()) {
        total_allocated_.fetchAndSubAcquire(it->second.size);
        allocations_.erase(it);
    }
}

MemoryLeakDetector::LeakReport MemoryLeakDetector::detectLeaks() {
    QMutexLocker locker(&tracking_mutex_);

    LeakReport report;
    report.report_time = QDateTime::currentDateTime();
    report.session_id = QUuid::createUuid().toString(QUuid::WithoutBraces);

    for (auto& [ptr, info] : allocations_) {
        // Consider allocations older than 5 minutes as potential leaks
        if (info.allocation_time.secsTo(QDateTime::currentDateTime()) > 300) {
            info.is_leaked = true;
            report.leaks.push_back(info);
            report.total_leaked_bytes += info.size;
        }
    }

    report.leak_count = static_cast<int>(report.leaks.size());

    return report;
}

std::vector<MemoryLeakDetector::AllocationInfo> MemoryLeakDetector::getActiveAllocations() const {
    QMutexLocker locker(&tracking_mutex_);

    std::vector<AllocationInfo> allocations;
    for (const auto& [ptr, info] : allocations_) {
        allocations.push_back(info);
    }

    return allocations;
}

qint64 MemoryLeakDetector::getTotalAllocatedMemory() const {
    return total_allocated_.load();
}

QJsonObject MemoryLeakDetector::generateLeakReport() const {
    auto report = const_cast<MemoryLeakDetector*>(this)->detectLeaks();

    QJsonObject json_report;
    json_report["session_id"] = report.session_id;
    json_report["report_time"] = report.report_time.toString(Qt::ISODate);
    json_report["total_leaked_bytes"] = static_cast<qint64>(report.total_leaked_bytes);
    json_report["leak_count"] = report.leak_count;

    QJsonArray leaks_array;
    for (const auto& leak : report.leaks) {
        QJsonObject leak_obj;
        leak_obj["address"] = QString::number(reinterpret_cast<qintptr>(leak.address), 16);
        leak_obj["size"] = static_cast<qint64>(leak.size);
        leak_obj["source_file"] = leak.source_file;
        leak_obj["source_line"] = leak.source_line;
        leak_obj["allocation_time"] = leak.allocation_time.toString(Qt::ISODate);
        leak_obj["stack_trace"] = leak.stack_trace;
        leaks_array.append(leak_obj);
    }
    json_report["leaks"] = leaks_array;

    return json_report;
}

void MemoryLeakDetector::exportLeakReport(const QString& file_path) const {
    QJsonObject report = generateLeakReport();
    QJsonDocument doc(report);

    QFile file(file_path);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        qDebug() << "Memory leak report exported to:" << file_path;
    } else {
        qWarning() << "Failed to export memory leak report to:" << file_path;
    }
}

} // namespace DeclarativeUI::Profiling
