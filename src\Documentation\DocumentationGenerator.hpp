#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QRegularExpression>
#include <QDateTime>
#include <QUrl>
#include <QProcess>
#include <QThread>
#include <QMutex>
#include <QReadWriteLock>
#include <QTimer>
#include <QFileSystemWatcher>
#include <QMetaObject>
#include <QMetaMethod>
#include <QMetaProperty>
#include <QMetaEnum>
#include <QMetaClassInfo>
#include <memory>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <functional>

namespace DeclarativeUI::Documentation {

/**
 * @brief Comprehensive documentation generator for DeclarativeUI framework
 * 
 * This system automatically generates:
 * - API documentation from source code annotations
 * - Component galleries with live examples
 * - Interactive documentation with code samples
 * - Cross-referenced documentation with search capabilities
 * - Multiple output formats (HTML, Markdown, JSON, PDF)
 */
class DocumentationGenerator : public QObject {
    Q_OBJECT

public:
    explicit DocumentationGenerator(QObject* parent = nullptr);
    ~DocumentationGenerator();

    // **Configuration Structures**
    struct GeneratorConfig {
        QString project_name = "DeclarativeUI Framework";
        QString project_version = "1.0.0";
        QString project_description = "Modern declarative UI framework for Qt";
        QString project_url = "https://github.com/DeclarativeUI/DeclarativeUI";
        QString author = "DeclarativeUI Team";
        QString license = "MIT";
        
        // Input configuration
        QStringList source_directories = {"src/"};
        QStringList include_patterns = {"*.hpp", "*.cpp", "*.h", "*.c"};
        QStringList exclude_patterns = {"moc_*", "ui_*", "qrc_*", "*/build/*", "*/.cache/*"};
        QStringList example_directories = {"examples/"};
        QStringList documentation_directories = {"docs/"};
        
        // Output configuration
        QString output_directory = "docs/generated";
        QStringList output_formats = {"html", "markdown", "json"};
        bool generate_component_gallery = true;
        bool generate_api_reference = true;
        bool generate_examples = true;
        bool generate_search_index = true;
        bool generate_cross_references = true;
        
        // Processing options
        bool parse_doxygen_comments = true;
        bool parse_qt_annotations = true;
        bool extract_examples = true;
        bool analyze_inheritance = true;
        bool generate_diagrams = true;
        bool include_private_members = false;
        bool include_implementation_details = false;
        
        // Styling and theming
        QString theme = "default";
        QString custom_css_file;
        QString logo_file;
        QJsonObject custom_styling;
        
        // Advanced features
        bool enable_live_examples = true;
        bool enable_code_highlighting = true;
        bool enable_interactive_demos = true;
        bool enable_performance_metrics = true;
        bool enable_accessibility_info = true;
    };

    struct DocumentationItem {
        enum Type {
            CLASS,
            STRUCT,
            ENUM,
            FUNCTION,
            METHOD,
            PROPERTY,
            SIGNAL,
            SLOT,
            NAMESPACE,
            MACRO,
            TYPEDEF,
            VARIABLE,
            EXAMPLE,
            TUTORIAL,
            GUIDE
        };

        Type type;
        QString name;
        QString qualified_name;
        QString brief_description;
        QString detailed_description;
        QString file_path;
        int line_number = 0;
        QString namespace_name;
        QString class_name;
        QStringList parameters;
        QString return_type;
        QStringList template_parameters;
        QStringList base_classes;
        QStringList derived_classes;
        QStringList see_also;
        QStringList examples;
        QStringList tags;
        QString since_version;
        QString deprecated_info;
        QJsonObject metadata;
        QJsonObject annotations;
    };

    struct ComponentInfo {
        QString name;
        QString category;
        QString description;
        QString class_name;
        QString header_file;
        QStringList properties;
        QStringList signal_list;
        QStringList slot_list;
        QStringList examples;
        QString preview_image;
        QJsonObject demo_config;
        QStringList dependencies;
        QString usage_example;
        QJsonObject styling_options;
    };

    struct ExampleInfo {
        QString name;
        QString title;
        QString description;
        QString category;
        QString file_path;
        QString main_file;
        QStringList source_files;
        QStringList dependencies;
        QString build_instructions;
        QString screenshot;
        QJsonObject metadata;
        bool is_interactive = false;
        bool is_live_demo = false;
    };

    // **Configuration Management**
    void setConfiguration(const GeneratorConfig& config);
    GeneratorConfig getConfiguration() const { return config_; }
    void loadConfigurationFromFile(const QString& config_file);
    void saveConfigurationToFile(const QString& config_file) const;

    // **Documentation Generation**
    bool generateDocumentation();
    bool generateAPIReference();
    bool generateComponentGallery();
    bool generateExamples();
    bool generateSearchIndex();
    bool generateCrossReferences();

    // **Source Code Analysis**
    std::vector<DocumentationItem> parseSourceFiles();
    std::vector<ComponentInfo> extractComponents();
    std::vector<ExampleInfo> extractExamples();
    QJsonObject analyzeInheritanceHierarchy();
    QJsonObject generateDependencyGraph();

    // **Content Processing**
    DocumentationItem parseClass(const QString& file_path, const QString& class_content);
    DocumentationItem parseFunction(const QString& file_path, const QString& function_content);
    DocumentationItem parseEnum(const QString& file_path, const QString& enum_content);
    QString parseDoxygenComment(const QString& comment);
    QJsonObject parseAnnotations(const QString& content);

    // **Output Generation**
    bool generateHTML();
    bool generateMarkdown();
    bool generateJSON();
    bool generatePDF();
    QString generateComponentHTML(const ComponentInfo& component);
    QString generateExampleHTML(const ExampleInfo& example);
    QString generateAPIHTML(const DocumentationItem& item);

    // **Template System**
    void setTemplateDirectory(const QString& template_dir);
    QString loadTemplate(const QString& template_name);
    QString processTemplate(const QString& template_content, const QJsonObject& data);
    void registerTemplateFunction(const QString& name, std::function<QString(const QJsonArray&)> func);

    // **Live Documentation**
    void startLiveDocumentation(int port = 8080);
    void stopLiveDocumentation();
    bool isLiveDocumentationRunning() const { return live_server_running_; }

    // **Incremental Updates**
    void enableIncrementalUpdates(bool enabled);
    void watchSourceFiles();
    void updateDocumentation(const QStringList& changed_files);

    // **Search and Indexing**
    void buildSearchIndex();
    QJsonArray searchDocumentation(const QString& query);
    QStringList getSuggestions(const QString& partial_query);

    // **Validation and Quality**
    QJsonObject validateDocumentation();
    QStringList findMissingDocumentation();
    QStringList findBrokenLinks();
    QJsonObject generateQualityReport();

    // **Export and Import**
    void exportDocumentation(const QString& export_file, const QString& format = "json");
    void importDocumentation(const QString& import_file);
    void mergeDocumentation(const QJsonObject& external_docs);

signals:
    void generationStarted();
    void generationProgress(int percentage, const QString& current_task);
    void generationFinished(bool success);
    void errorOccurred(const QString& error_message);
    void warningIssued(const QString& warning_message);
    void fileProcessed(const QString& file_path);
    void componentExtracted(const ComponentInfo& component);
    void exampleFound(const ExampleInfo& example);
    void configurationChanged();

private slots:
    void onSourceFileChanged(const QString& file_path);
    void onConfigurationChanged();
    void updateIncrementalDocumentation();

private:
    // **Core processing methods**
    void initializeGenerator();
    void setupOutputDirectories();
    void copyStaticAssets();
    void generateNavigationStructure();
    void generateSitemap();

    // **File processing**
    QStringList findSourceFiles();
    QStringList findExampleFiles();
    QString readFileContent(const QString& file_path);
    bool writeFileContent(const QString& file_path, const QString& content);

    // **Parsing utilities**
    QStringList extractIncludes(const QString& content);
    QStringList extractNamespaces(const QString& content);
    QStringList extractClasses(const QString& content);
    QStringList extractFunctions(const QString& content);
    QStringList extractEnums(const QString& content);
    QString extractClassDocumentation(const QString& content, const QString& class_name);
    QString extractFunctionDocumentation(const QString& content, const QString& function_name);

    // **Qt Meta-Object analysis**
    QJsonObject analyzeQtClass(const QMetaObject* meta_object);
    QJsonArray extractQtProperties(const QMetaObject* meta_object);
    QJsonArray extractQtMethods(const QMetaObject* meta_object);
    QJsonArray extractQtSignals(const QMetaObject* meta_object);
    QJsonArray extractQtSlots(const QMetaObject* meta_object);
    QJsonArray extractQtEnums(const QMetaObject* meta_object);

    // **Content generation helpers**
    QString generateTableOfContents(const std::vector<DocumentationItem>& items);
    QString generateBreadcrumbs(const QString& current_path);
    QString generateCrossReferenceLinks(const QString& content);
    QString generateCodeHighlighting(const QString& code, const QString& language = "cpp");
    QString generateDiagram(const QString& diagram_type, const QJsonObject& data);

    // **Template processing**
    QString substituteVariables(const QString& template_content, const QJsonObject& variables);
    QString processConditionals(const QString& content, const QJsonObject& context);
    QString processLoops(const QString& content, const QJsonObject& context);
    QString processIncludes(const QString& content, const QString& base_path);

    // **Live server functionality**
    void setupLiveServer();
    void handleLiveRequest(const QString& path, const QJsonObject& parameters);
    QString generateLiveResponse(const QString& content_type, const QJsonObject& data);

    // **Utility methods**
    QString sanitizeFileName(const QString& name);
    QString generateUniqueId(const QString& base_name);
    QString formatDateTime(const QDateTime& date_time);
    QString escapeHtml(const QString& text);
    QString unescapeHtml(const QString& html);
    QJsonObject createMetadata(const QString& file_path);

    // **Configuration and state**
    GeneratorConfig config_;
    QString template_directory_;
    std::unordered_map<QString, std::function<QString(const QJsonArray&)>> template_functions_;

    // **Parsed data storage**
    mutable QReadWriteLock data_lock_;
    std::vector<DocumentationItem> documentation_items_;
    std::vector<ComponentInfo> components_;
    std::vector<ExampleInfo> examples_;
    QJsonObject inheritance_hierarchy_;
    QJsonObject dependency_graph_;
    QJsonObject search_index_;

    // **File watching and incremental updates**
    std::unique_ptr<QFileSystemWatcher> file_watcher_;
    std::unordered_set<QString> watched_files_;
    std::atomic<bool> incremental_updates_enabled_{false};
    std::unique_ptr<QTimer> update_timer_;

    // **Live documentation server**
    std::atomic<bool> live_server_running_{false};
    int live_server_port_ = 8080;
    std::unique_ptr<QThread> server_thread_;

    // **Processing state**
    std::atomic<bool> generation_in_progress_{false};
    std::atomic<int> current_progress_{0};
    QString current_task_;
    mutable QMutex state_mutex_;

    // **Regular expressions for parsing**
    QRegularExpression class_regex_;
    QRegularExpression function_regex_;
    QRegularExpression enum_regex_;
    QRegularExpression comment_regex_;
    QRegularExpression doxygen_regex_;
    QRegularExpression include_regex_;
    QRegularExpression namespace_regex_;

    // **Statistics and metrics**
    struct GenerationStatistics {
        int total_files_processed = 0;
        int classes_documented = 0;
        int functions_documented = 0;
        int examples_found = 0;
        int components_extracted = 0;
        qint64 generation_time_ms = 0;
        QDateTime last_generation;
        QStringList warnings;
        QStringList errors;
    } stats_;
};

/**
 * @brief Component gallery generator for interactive documentation
 */
class ComponentGalleryGenerator : public QObject {
    Q_OBJECT

public:
    explicit ComponentGalleryGenerator(QObject* parent = nullptr);

    struct GalleryConfig {
        QString title = "Component Gallery";
        QString description = "Interactive showcase of DeclarativeUI components";
        QString output_directory = "docs/gallery";
        bool generate_live_demos = true;
        bool include_source_code = true;
        bool enable_customization = true;
        QString theme = "default";
    };

    void setConfiguration(const GalleryConfig& config);
    bool generateGallery(const std::vector<ComponentInfo>& components);
    QString generateComponentDemo(const ComponentInfo& component);
    QString generateInteractiveDemo(const ComponentInfo& component);

private:
    GalleryConfig config_;
    QString generateDemoHTML(const ComponentInfo& component);
    QString generateCustomizationPanel(const ComponentInfo& component);
};

/**
 * @brief Example documentation generator
 */
class ExampleDocumentationGenerator : public QObject {
    Q_OBJECT

public:
    explicit ExampleDocumentationGenerator(QObject* parent = nullptr);

    bool generateExampleDocumentation(const std::vector<ExampleInfo>& examples);
    QString generateExamplePage(const ExampleInfo& example);
    QString generateExampleIndex(const std::vector<ExampleInfo>& examples);

private:
    QString extractExampleDescription(const QString& file_path);
    QStringList extractExampleSteps(const QString& content);
    QString generateRunInstructions(const ExampleInfo& example);
};

} // namespace DeclarativeUI::Documentation
